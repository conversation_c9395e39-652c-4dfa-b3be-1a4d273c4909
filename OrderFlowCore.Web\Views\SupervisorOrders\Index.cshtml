@model OrderFlowCore.Web.ViewModels.SupervisorOrdersViewModel
@{
    ViewData["Title"] = "مشرف الطلبات";
}

<div class="container-fluid dashboard-container">
    <!-- Welcome Header -->
    <div class="welcome-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-2 text-center">
                <i class="fas fa-user-shield fa-4x text-primary"></i>
            </div>
            <div class="col-md-10">
                <h2 class="mb-1">مرحباً، مشرف الطلبات!</h2>
                <p class="mb-1"><i class="fas fa-user-tag me-2"></i>الدور: مشرف الطلبات</p>
                <p class="mb-0"><i class="fas fa-tasks me-2"></i>إدارة ومعالجة الطلبات المحولة</p>
            </div>
        </div>
    </div>

    <!-- Navigation Cards -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <div class="card-body text-center">
                    <h5 class="card-title">📝 معالجة الطلبات</h5>
                    <p class="card-text">معالجة الطلبات المحولة واتخاذ الإجراءات المناسبة</p>
                    <a asp-action="ProcessOrder" class="btn btn-success">
                        <i class="fas fa-tasks me-2"></i> معالجة الطلبات
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <div class="card-body text-center">
                    <h5 class="card-title">📋 متابعة السجلات</h5>
                    <p class="card-text">متابعة السجلات الخاصة وإدارة البيانات</p>
                    <a asp-action="FollowUpRecords" class="btn btn-dark">
                        <i class="fas fa-clipboard-list me-2"></i> متابعة السجلات
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <div class="card-body text-center">
                    <h5 class="card-title">📊 لوحة الإحصائيات</h5>
                    <p class="card-text">عرض إحصائيات الطلبات والأداء</p>
                    <a asp-action="Dashboard" class="btn btn-info">
                        <i class="fas fa-chart-bar me-2"></i> عرض الإحصائيات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card primary">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-number">@(Model?.OrderNumbers?.Count() ?? 0)</div>
                        <div>الطلبات المتاحة</div>
                    </div>
                    <i class="fas fa-file-alt stat-icon"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-number">0</div>
                        <div>قيد المعالجة</div>
                    </div>
                    <i class="fas fa-clock stat-icon"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-number">0</div>
                        <div>مكتملة اليوم</div>
                    </div>
                    <i class="fas fa-check-circle stat-icon"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card danger">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-number">@(Model?.FollowUpRecords?.Count() ?? 0)</div>
                        <div>سجلات المتابعة</div>
                    </div>
                    <i class="fas fa-clipboard-list stat-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>الإجراءات السريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="d-grid">
                                <a asp-action="ProcessOrder" class="btn btn-outline-success btn-lg">
                                    <i class="fas fa-play-circle me-2"></i>
                                    بدء معالجة الطلبات
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-grid">
                                <a asp-action="FollowUpRecords" class="btn btn-outline-dark btn-lg">
                                    <i class="fas fa-search me-2"></i>
                                    البحث في السجلات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
