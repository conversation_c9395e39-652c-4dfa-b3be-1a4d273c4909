using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Web.ViewModels;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Web.Controllers
{
    public class SupervisorOrdersController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly ISupervisorsFollowUpService _followUpService;

        public SupervisorOrdersController(IOrderService orderService, ISupervisorsFollowUpService followUpService)
        {
            _orderService = orderService;
            _followUpService = followUpService;
        }

        [HttpGet]
        public async Task<IActionResult> Index(int? selectedOrderId, bool showRecordsPanel = false, string successMessage = null, string errorMessage = null)
        {
            var viewModel = new SupervisorOrdersViewModel
            {
                SelectedOrderId = selectedOrderId,
                ShowRecordsPanel = showRecordsPanel,
                SuccessMessage = successMessage,
                ErrorMessage = errorMessage
            };

            // Load order numbers for dropdown
            var ordersResult = await _orderService.GetHRCoordinatorOrdersAsync();
            if (ordersResult.IsSuccess)
            {
                viewModel.OrderNumbers = ordersResult.Data.Select(o => new SelectListItem
                {
                    Value = o.Id.ToString(),
                    Text = string.IsNullOrEmpty(o.EmployeeName) ? o.Id.ToString() : $"{o.Id} | {o.EmployeeName}"
                }).ToList();
            }
            else
            {
                viewModel.ErrorMessage = ordersResult.Message;
            }

            // Load selected order details
            if (selectedOrderId.HasValue)
            {
                var detailsResult = await _orderService.GetOrderDetailsAsync(selectedOrderId.Value);
                if (detailsResult.IsSuccess)
                {
                    viewModel.OrderDetails = detailsResult.Data;
                }
                else
                {
                    viewModel.ErrorMessage = detailsResult.Message;
                }
            }

            // Load follow-up records for this supervisor
            var supervisorId = User.Identity?.Name;
            if (!string.IsNullOrEmpty(supervisorId))
            {
                viewModel.FollowUpRecords = await _followUpService.GetBySupervisorAsync(supervisorId);
            }

            return View(viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> Dashboard()
        {
            var viewModel = new SupervisorOrdersViewModel();

            // Load order numbers for statistics
            var ordersResult = await _orderService.GetHRCoordinatorOrdersAsync();
            if (ordersResult.IsSuccess)
            {
                viewModel.OrderNumbers = ordersResult.Data.Select(o => new SelectListItem
                {
                    Value = o.Id.ToString(),
                    Text = string.IsNullOrEmpty(o.EmployeeName) ? o.Id.ToString() : $"{o.Id} | {o.EmployeeName}"
                }).ToList();
            }

            // Load follow-up records for statistics
            var supervisorId = User.Identity?.Name;
            if (!string.IsNullOrEmpty(supervisorId))
            {
                viewModel.FollowUpRecords = await _followUpService.GetBySupervisorAsync(supervisorId);
            }

            return View(viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> ProcessOrder(int? selectedOrderId, string successMessage = null, string errorMessage = null)
        {
            var viewModel = new SupervisorOrdersViewModel
            {
                SelectedOrderId = selectedOrderId,
                SuccessMessage = successMessage,
                ErrorMessage = errorMessage
            };

            // Load order numbers for dropdown
            var ordersResult = await _orderService.GetHRCoordinatorOrdersAsync();
            if (ordersResult.IsSuccess)
            {
                viewModel.OrderNumbers = ordersResult.Data.Select(o => new SelectListItem
                {
                    Value = o.Id.ToString(),
                    Text = string.IsNullOrEmpty(o.EmployeeName) ? o.Id.ToString() : $"{o.Id} | {o.EmployeeName}"
                }).ToList();
            }
            else
            {
                viewModel.ErrorMessage = ordersResult.Message;
            }

            // Load selected order details
            if (selectedOrderId.HasValue)
            {
                var detailsResult = await _orderService.GetOrderDetailsAsync(selectedOrderId.Value);
                if (detailsResult.IsSuccess)
                {
                    viewModel.OrderDetails = detailsResult.Data;
                }
                else
                {
                    viewModel.ErrorMessage = detailsResult.Message;
                }
            }

            return View("ProcessOrder", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> FollowUpRecords(string successMessage = null, string errorMessage = null)
        {
            var viewModel = new SupervisorOrdersViewModel
            {
                ShowRecordsPanel = true,
                SuccessMessage = successMessage,
                ErrorMessage = errorMessage
            };

            // Load follow-up records for this supervisor
            var supervisorId = User.Identity?.Name;
            if (!string.IsNullOrEmpty(supervisorId))
            {
                viewModel.FollowUpRecords = await _followUpService.GetBySupervisorAsync(supervisorId);
            }

            return View("FollowUpRecords", viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmOrder(int orderId)
        {
            var result = await _orderService.SubmitOrderByHrCoordinatorAsync(orderId, "", new List<string>(), User.Identity?.Name);
            return RedirectToAction(nameof(Index), new { selectedOrderId = orderId, successMessage = result.Message, errorMessage = result.IsSuccess ? null : result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> NeedsAction(int orderId, string actionRequired)
        {
            var result = await _orderService.OrderNeedsActionByCoordinatorAsync(orderId, actionRequired, User.Identity?.Name);
            return RedirectToAction(nameof(Index), new { selectedOrderId = orderId, successMessage = result.IsSuccess ? result.Message : null, errorMessage = result.IsSuccess ? null : result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RejectOrder(int orderId, string rejectReason)
        {
            var result = await _orderService.RejectOrderByHRCoordinatorAsync(orderId, rejectReason, User.Identity?.Name);
            return RedirectToAction(nameof(Index), new { selectedOrderId = orderId, successMessage = result.IsSuccess ? result.Message : null, errorMessage = result.IsSuccess ? null : result.Message });
        }

        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int orderId)
        {
            var result = await _orderService.DownloadOrderAttachmentsZipAsync(orderId);
            if (!result.IsSuccess)
            {
                return RedirectToAction(nameof(Index), new { selectedOrderId = orderId, errorMessage = result.Message });
            }
            var detailsResult = await _orderService.GetOrderDetailsAsync(orderId);
            var fileName = detailsResult.IsSuccess ? $"مرفقات_طلب_{orderId}_{detailsResult.Data.EmployeeName}.zip" : $"مرفقات_طلب_{orderId}.zip";
            return File(result.Data, "application/zip", fileName);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddFollowUpRecord(SupervisorOrdersViewModel model)
        {
            var supervisorId = User.Identity?.Name;
            var dto = new SupervisorsFollowUpDto
            {
                SupervisorId = supervisorId,
                CivilRecord = model.CivilRegistry,
                OwnerName = model.OwnerName,
                SpecialProcedure = model.SpecialProcedure
            };
            await _followUpService.AddAsync(dto);
            return RedirectToAction(nameof(Index), new { showRecordsPanel = true, successMessage = "تمت إضافة السجل بنجاح" });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditFollowUpRecord(SupervisorOrdersViewModel model)
        {
            var supervisorId = User.Identity?.Name;
            var dto = new SupervisorsFollowUpDto
            {
                SupervisorId = supervisorId,
                CivilRecord = model.EditCivilRegistry,
                OwnerName = model.EditOwnerName,
                SpecialProcedure = model.EditSpecialProcedure
            };
            await _followUpService.UpdateAsync(dto);
            return RedirectToAction(nameof(Index), new { showRecordsPanel = true, successMessage = "تم تحديث السجل بنجاح" });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteFollowUpRecord(string civilRecord)
        {
            var supervisorId = User.Identity?.Name;
            await _followUpService.DeleteAsync(supervisorId, civilRecord);
            return RedirectToAction(nameof(Index), new { showRecordsPanel = true, successMessage = "تم حذف السجل بنجاح" });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ImportFollowUpRecords()
        {
            var supervisorId = User.Identity?.Name;
            var file = Request.Form.Files["csvFile"];
            if (file == null || file.Length == 0)
            {
                return RedirectToAction(nameof(Index), new { showRecordsPanel = true, errorMessage = "يرجى اختيار ملف CSV صالح" });
            }
            using (var stream = file.OpenReadStream())
            {
                var count = await _followUpService.ImportAsync(supervisorId, stream);
                return RedirectToAction(nameof(Index), new { showRecordsPanel = true, successMessage = $"تم استيراد {count} سجل بنجاح" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> ExportFollowUpRecords()
        {
            var supervisorId = User.Identity?.Name;
            var data = await _followUpService.ExportAsync(supervisorId);
            return File(data, "text/csv", "FollowUpRecords.csv");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteAllFollowUpRecords()
        {
            var supervisorId = User.Identity?.Name;
            await _followUpService.DeleteAllAsync(supervisorId);
            return RedirectToAction(nameof(Index), new { showRecordsPanel = true, successMessage = "تم حذف جميع السجلات بنجاح" });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddFollowUpRecordAjax([FromBody] SupervisorsFollowUpDto dto)
        {
            try
            {
                dto.SupervisorId = User.Identity?.Name;
                await _followUpService.AddAsync(dto);
                return Json(new { success = true, message = "تمت إضافة السجل بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditFollowUpRecordAjax([FromBody] SupervisorsFollowUpDto dto)
        {
            try
            {
                dto.SupervisorId = User.Identity?.Name;
                await _followUpService.UpdateAsync(dto);
                return Json(new { success = true, message = "تم تحديث السجل بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteFollowUpRecordAjax([FromBody] SupervisorsFollowUpDto dto)
        {
            try
            {
                var supervisorId = User.Identity?.Name;
                await _followUpService.DeleteAsync(supervisorId, dto.CivilRecord);
                return Json(new { success = true, message = "تم حذف السجل بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }
    }
} 