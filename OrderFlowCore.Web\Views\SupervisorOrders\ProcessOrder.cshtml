@model OrderFlowCore.Web.ViewModels.SupervisorOrdersViewModel
@{
    ViewData["Title"] = "معالجة الطلبات";
}

@Html.AntiForgeryToken()

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>معالجة الطلبات</h2>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة الرئيسية
                </a>
            </div>

            <!-- Message Container for validation and operation feedback -->
            <div id="processOrderMessageContainer"></div>

            <!-- Order Selection Section -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-search me-2"></i>اختيار الطلب</h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <label for="orderSelect" class="form-label fw-bold">📋 رقم الطلب:</label>
                            @Html.DropDownListFor(m => m.SelectedOrderId, Model.OrderNumbers, "اختر الطلب من القائمة",
                                new { @class = "form-select", @id = "orderSelect"})
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                <p class="mb-2">اختر طلباً لعرض تفاصيله واتخاذ الإجراء المناسب</p>
                                <a asp-action="DownloadAttachments" asp-route-orderId="@Model.SelectedOrderId" class="btn btn-outline-primary">
                                    <i class="fas fa-download"></i> تحميل مرفقات الطلب
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Processing Section -->
            <div class="row">
                <!-- Right Column: Actions -->
                <div class="col-lg-4">
                    <!-- Primary Actions -->
                    <div class="card mb-3 border-success">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-play-circle"></i> الإجراءات الرئيسية</h6>
                        </div>
                        <div class="card-body">
                            <form id="confirmOrderForm" asp-action="ConfirmOrder" method="post">
                                @Html.AntiForgeryToken()
                                <input type="hidden" name="orderId" value="@Model.SelectedOrderId" />
                                <button type="button" class="btn btn-success btn-lg w-100 mb-3" id="confirmOrderBtn">
                                    <i class="fas fa-check-circle"></i> اعتماد الطلب
                                </button>
                            </form>

                            <form id="needsActionForm" asp-action="NeedsAction" method="post">
                                @Html.AntiForgeryToken()
                                <input type="hidden" name="orderId" value="@Model.SelectedOrderId" />
                                <input type="text" name="actionRequired" id="actionRequiredInput" class="form-control mb-2" placeholder="الإجراءات المطلوبة" required minlength="2" maxlength="100" />
                                <button type="button" class="btn btn-warning btn-lg w-100 mb-3" id="needsActionBtn">
                                    <i class="fas fa-exclamation-triangle"></i> يتطلب إجراءات
                                </button>
                            </form>

                            <form id="rejectOrderForm" asp-action="RejectOrder" method="post">
                                @Html.AntiForgeryToken()
                                <input type="hidden" name="orderId" value="@Model.SelectedOrderId" />
                                <input type="text" name="rejectReason" id="rejectReasonInput" class="form-control mb-2" placeholder="سبب الإعادة" required minlength="2" maxlength="100" />
                                <button type="button" class="btn btn-danger btn-lg w-100" id="rejectOrderBtn">
                                    <i class="fas fa-times-circle"></i> إعادة الطلب
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mb-3">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0"><i class="fas fa-bolt"></i> إجراءات سريعة</h6>
                        </div>
                        <div class="card-body">
                            <a asp-action="DownloadAttachments" asp-route-orderId="@Model.SelectedOrderId" class="btn btn-outline-secondary w-100 mb-2">
                                <i class="fas fa-download"></i> تحميل المرفقات
                            </a>
                            <a asp-action="FollowUpRecords" class="btn btn-outline-dark w-100">
                                <i class="fas fa-clipboard-list"></i> إدارة السجلات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Left Column: Order Details -->
                <div class="col-lg-8">
                    <!-- Order Details Card -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0"><i class="fas fa-file-alt"></i> تفاصيل الطلب</h5>
                        </div>
                        <div class="card-body">
                            @await Html.PartialAsync("_OrderDetailsPartial")
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Containers -->
            <div id="messageContainer" class="mt-3"></div>
            <div id="actionRequireMessageContainer" class="mt-3"></div>

            <!-- Loading -->
            <div id="loading" class="loading text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل تفاصيل الطلب...</p>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Order Modal -->
<div class="modal fade" id="confirmOrderModal" tabindex="-1" aria-labelledby="confirmOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmOrderModalLabel">تأكيد الاعتماد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من اعتماد هذا الطلب؟</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmOrderModalBtn">تأكيد</button>
            </div>
        </div>
    </div>
</div>
<!-- Needs Action Modal -->
<div class="modal fade" id="needsActionModal" tabindex="-1" aria-labelledby="needsActionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="needsActionModalLabel">تأكيد الإجراء المطلوب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إرسال هذا الطلب كـ "يتطلب إجراءات"؟</p>
                <div id="needsActionModalReason" class="text-muted"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" id="needsActionModalBtn">تأكيد</button>
            </div>
        </div>
    </div>
</div>
<!-- Reject Order Modal -->
<div class="modal fade" id="rejectOrderModal" tabindex="-1" aria-labelledby="rejectOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectOrderModalLabel">تأكيد إعادة الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إعادة هذا الطلب؟</p>
                <div id="rejectOrderModalReason" class="text-muted"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="rejectOrderModalBtn">تأكيد</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/shared-utils.js"></script>
    <script src="~/js/orderDetailsModule.js"></script>
    <script>
        // Show success/error messages from TempData
        @if (TempData["SuccessMessage"] != null)
        {
            <text>OrderDetailsModule.showMessage('@TempData["SuccessMessage"]', 'success');</text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>OrderDetailsModule.showMessage('@TempData["ErrorMessage"]', 'error');</text>
        }
    </script>

    <style>
        /* Custom styling for better visual hierarchy */
        .btn-lg {
            padding: 0.75rem 1rem;
            font-size: 1.1rem;
        }

        .card-header h5, .card-header h6 {
            margin-bottom: 0;
        }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        /* Visual separation for different action types */
        .border-success {
            border-width: 2px !important;
        }

        /* Improve button hover states */
        .btn-outline-secondary:hover {
            background-color: #6c757d;
            color: white;
        }

        /* Icon spacing */
        .btn i {
            margin-right: 0.5rem;
        }

        /* Loading animation */
        .loading {
            opacity: 0.8;
        }
    </style>
}